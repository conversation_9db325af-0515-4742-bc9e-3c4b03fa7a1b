<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import Header from '$lib/components/Header.svelte';
  import Footer from '$lib/components/Footer.svelte';
  import * as Sonner from '$lib/components/ui/sonner';
  import { setupTrackingForDev, setupTrackingForProd } from '$lib/tracking';
  import { setupGlobalErrorTracking } from '$lib/tracking/errors/svelte-helpers';
  import '../app.css';
  
  let { children } = $props();

  onMount(() => {
    if (browser) {
      // 🎯 TRACKING SYSTEM SETUP
      // Detecta automaticamente o ambiente baseado no hostname
      const isDev = window.location.hostname === 'localhost' || 
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.includes('dev');

      if (isDev) {
        // Configuração para desenvolvimento
        // - Debug mode ativado
        // - Flush imediato para testes
        // - Logs detalhados no console
        setupTrackingForDev('{{PROJECT_NAME}}');
        console.log('🎯 Tracking system initialized for DEVELOPMENT');

        // 🚨 ERROR TRACKING SETUP - MODO PICA DAS GALAXIAS!
        // Setup básico para development (sem Discord)
        setupGlobalErrorTracking(
          undefined, // Sem Discord por padrão
          '{{PROJECT_NAME}}',
          'development'
        );
        console.log('🚨 Error tracking initialized for DEVELOPMENT');
      } else {
        // Configuração para produção
        // - Performance otimizada
        // - Batch processing
        // - IMPORTANTE: Substitua pela URL do seu PocketBase em produção
        setupTrackingForProd('{{PROJECT_NAME}}', 'https://your-pocketbase-url.com');
        console.log('🎯 Tracking system initialized for PRODUCTION');

        // 🚨 ERROR TRACKING SETUP - PRODUÇÃO
        // Para produção, adicione sua Discord webhook URL se desejar
        setupGlobalErrorTracking(
          // 'https://discord.com/api/webhooks/YOUR_WEBHOOK_URL', // Descomente e adicione sua URL
          undefined, // Sem Discord por padrão
          '{{PROJECT_NAME}}',
          'production'
        );
        console.log('🚨 Error tracking initialized for PRODUCTION');
      }

      // 📊 TRACKING AUTOMÁTICO ATIVO:
      // ✅ Navegação entre páginas
      // ✅ Tempo de permanência
      // ✅ Profundidade de scroll
      // ✅ Cliques em elementos
      // ✅ Interações com formulários
      // ✅ Quality score por página
      // 🚨 Error tracking com 3 tipos de captura

      // 🔧 PARA CUSTOMIZAR:
      // Visite /examples/tracking para ver exemplos de uso
      // Visite /examples/error-demo para testar error tracking
      // Ou consulte a documentação em src/lib/tracking/README.md
    }
  });
</script>

<div class="min-h-screen flex flex-col">
  <Header />
  
  <main class="flex-1 overflow-y-auto">
    {@render children()}
  </main>
  
  <Footer />
</div>

<Sonner.Toaster />

<!--
  🎯 TRACKING SYSTEM INTEGRADO

  O sistema de tracking está ativo e coletando dados automaticamente.

  📊 Dados coletados:
  - Páginas visitadas e tempo de permanência
  - Interações do usuário (cliques, scroll, formulários)
  - Quality score baseado em engajamento
  - Sessions e métricas de uso
  - 🚨 Erros capturados automaticamente (3 tipos)

  🔧 Para usar tracking manual:
  import { trackEvent, trackButtonClick } from '$lib/tracking';
  import { safeCall, logError } from '$lib/tracking/errors/svelte-helpers';

  trackEvent('user_action', { action: 'save_document' });
  trackButtonClick('save-btn', 'Save Document');

  // Error tracking
  const result = await safeCall(() => apiCall(), 'api_context');
  logError(error, 'manual_context', { additionalData });

  📚 Mais exemplos em:
  - /examples/tracking (tracking básico)
  - /examples/error-demo (error tracking)
  - /examples/error-origin-test (rastreamento de origem)
-->