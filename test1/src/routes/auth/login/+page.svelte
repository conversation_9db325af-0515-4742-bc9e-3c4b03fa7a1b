<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import * as Card from '$lib/components/ui/card';
  import { authStore } from '$lib/stores/auth.store.svelte';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';
  
  let email = $state('');
  let password = $state('');
  let loading = $state(false);
  
  async function handleLogin() {
    loading = true;
    
    try {
      await authStore.login(email, password);
      toast.success('Login realizado com sucesso!');
      goto('/');
    } catch (error) {
      toast.error('Erro ao fazer login. Verifique suas credenciais.');
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Login | MeuApp</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <div class="max-w-md mx-auto">
    <Card.Root>
      <Card.Header>
        <Card.Title>Login</Card.Title>
        <Card.Description>
          Entre com sua conta para continuar
        </Card.Description>
      </Card.Header>
      
      <Card.Content>
        <form onsubmit={(e) => { e.preventDefault(); handleLogin(); }} class="space-y-4">
          <div class="space-y-2">
            <Label for="email">Email</Label>
            <Input
              id="email"
              type="email"
              bind:value={email}
              required
              placeholder="<EMAIL>"
            />
          </div>
          
          <div class="space-y-2">
            <Label for="password">Senha</Label>
            <Input
              id="password"
              type="password"
              bind:value={password}
              required
              placeholder="••••••••"
            />
          </div>
          
          <Button type="submit" class="w-full" disabled={loading}>
            {loading ? 'Entrando...' : 'Entrar'}
          </Button>
        </form>
      </Card.Content>
      
      <Card.Footer>
        <p class="text-sm text-center w-full text-muted-foreground">
          Não tem uma conta?{' '}
          <a href="/auth/register" class="text-primary hover:underline">
            Criar conta
          </a>
        </p>
      </Card.Footer>
    </Card.Root>
  </div>
</div>