// Types relacionados à autenticação
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  created: string;
  updated: string;
  verified: boolean;
  emailVisibility: boolean;
}

export interface AuthResponse {
  token: string;
  record: User;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData extends LoginData {
  passwordConfirm: string;
  name?: string;
}