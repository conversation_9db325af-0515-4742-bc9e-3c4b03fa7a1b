// Simplified tracking system with correct types

import { browser } from '$app/environment';
import { page } from '$app/stores';
import type { TrackingConfig, SimpleTrackingEvent } from '../types/tracking.types';
import { TrackingStorageClient } from './storage-client';
import { SessionManager } from './session-manager';
import { DEFAULT_PAGE_THRESHOLDS } from '../config/page-thresholds';

/**
 * Simplified tracking system - monitors user behavior and engagement
 */
export class UserTracker {
    // Core tracking state
    private _isActive = $state(false);
    private _currentPage = $state<string | null>(null);
    private _pageStartTime = $state<number | null>(null);
    private _scrollDepth = $state(0);
    private _maxScrollDepth = $state(0);
    private _interactionCount = $state(0);

    // Core services
    private storageClient: TrackingStorageClient;
    private sessionManager: SessionManager;
    private config: TrackingConfig;

    // Event handlers
    private eventHandlers: (() => void)[] = [];

    constructor(config: Partial<TrackingConfig> = {}) {
        this.config = {
            projectId: 'default-project',
            trackingUrl: 'http://localhost:8090',
            enablePageTracking: true,
            enableInteractionTracking: true,
            enableFormTracking: true,
            enableScrollTracking: true,
            enableNavigationTracking: true,
            enableClickTracking: true,
            enableAutoTracking: true,
            batchSize: 10,
            flushInterval: 30000,
            sessionTimeout: 30 * 60 * 1000,
            pageThresholds: DEFAULT_PAGE_THRESHOLDS,
            debugMode: false,
            ...config
        };

        this.storageClient = new TrackingStorageClient(this.config.trackingUrl);
        this.sessionManager = new SessionManager();

        if (browser) {
            this.initialize();
        }
    }

    // Reactive getters
    get isActive() {
        return this._isActive;
    }

    get currentPage() {
        return this._currentPage;
    }

    get pageMetrics() {
        if (!this._currentPage || !this._pageStartTime) return null;

        return {
            timeSpent: Date.now() - this._pageStartTime,
            scrollDepth: this._scrollDepth,
            maxScrollDepth: this._maxScrollDepth,
            interactions: this._interactionCount,
            lastInteraction: Date.now()
        };
    }

    /**
     * Initialize tracking system
     */
    private initialize(): void {
        try {
            // Start session
            this.sessionManager.startSession();
            this._isActive = true;

            // Setup tracking
            this.setupPageTracking();
            this.setupInteractionTracking();
            this.setupScrollTracking();

            if (this.config.debugMode) {
                console.log('🎯 User tracking initialized successfully');
            }
        } catch (error) {
            console.error('❌ Failed to initialize tracking:', error);
        }
    }

    /**
     * Setup page tracking
     */
    private setupPageTracking(): void {
        if (!this.config.enablePageTracking) return;

        // Track initial page
        this.trackPageEnter(window.location.pathname);

        // Listen for navigation changes using SvelteKit page store
        let previousPath: string | null = null;

        const unsubscribe = page.subscribe((currentPage) => {
            if (browser && currentPage.url.pathname !== previousPath) {
                if (previousPath) {
                    this.trackPageExit(previousPath);
                }
                this.trackPageEnter(currentPage.url.pathname);
                previousPath = currentPage.url.pathname;
            }
        });

        this.eventHandlers.push(unsubscribe);
    }

    /**
     * Track page enter
     */
    private trackPageEnter(path: string): void {
        try {
            // End previous page if exists
            if (this._currentPage && this._pageStartTime) {
                this.trackPageExit(this._currentPage);
            }

            // Start new page tracking
            this._currentPage = path;
            this._pageStartTime = Date.now();
            this._scrollDepth = 0;
            this._maxScrollDepth = 0;
            this._interactionCount = 0;

            const session = this.sessionManager.currentSession;
            if (!session) return;

            const event: SimpleTrackingEvent = {
                id: crypto.randomUUID(),
                type: 'page_enter',
                timestamp: new Date().toISOString(),
                sessionId: session.sessionId,
                userId: session.userId,
                metadata: {
                    path,
                    referrer: document.referrer || undefined,
                    userAgent: navigator.userAgent,
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    }
                }
            };

            this.storageClient.saveEvent(this.convertToTrackingEvent(event));
            this.sessionManager.updateActivity();

            if (this.config.debugMode) {
                console.log(`📄 Page enter tracked: ${path}`);
            }
        } catch (error) {
            console.error('❌ Failed to track page enter:', error);
        }
    }

    /**
     * Track page exit
     */
    private trackPageExit(path: string): void {
        try {
            if (!this._pageStartTime) return;

            const timeSpent = Date.now() - this._pageStartTime;
            const threshold = this.config.pageThresholds[path] || this.config.pageThresholds.default;

            const session = this.sessionManager.currentSession;
            if (!session) return;

            const event: SimpleTrackingEvent = {
                id: crypto.randomUUID(),
                type: 'page_exit',
                timestamp: new Date().toISOString(),
                sessionId: session.sessionId,
                userId: session.userId,
                metadata: {
                    path,
                    timeSpent,
                    expectedTime: threshold,
                    engagementRatio: timeSpent / threshold,
                    scrollDepth: this._maxScrollDepth,
                    interactions: this._interactionCount,
                    qualityScore: this.calculateQualityScore(timeSpent, threshold, this._maxScrollDepth, this._interactionCount)
                }
            };

            this.storageClient.saveEvent(this.convertToTrackingEvent(event));

            if (this.config.debugMode) {
                console.log(`📄 Page exit tracked: ${path} (${timeSpent}ms)`);
            }
        } catch (error) {
            console.error('❌ Failed to track page exit:', error);
        }
    }

    /**
     * Setup interaction tracking
     */
    private setupInteractionTracking(): void {
        if (!this.config.enableInteractionTracking) return;

        const clickHandler = (event: MouseEvent) => {
            this.trackClick(event);
        };

        document.addEventListener('click', clickHandler, true);
        this.eventHandlers.push(() => document.removeEventListener('click', clickHandler, true));
    }

    /**
     * Track click interaction
     */
    private trackClick(event: MouseEvent): void {
        try {
            this._interactionCount++;

            const target = event.target as HTMLElement;
            const session = this.sessionManager.currentSession;
            if (!session) return;

            const trackingEvent: SimpleTrackingEvent = {
                id: crypto.randomUUID(),
                type: 'interaction',
                timestamp: new Date().toISOString(),
                sessionId: session.sessionId,
                userId: session.userId,
                metadata: {
                    interactionType: 'click',
                    path: this._currentPage,
                    element: {
                        tagName: target.tagName.toLowerCase(),
                        id: target.id || undefined,
                        className: target.className || undefined,
                        textContent: target.textContent?.slice(0, 100) || undefined
                    },
                    position: {
                        x: event.clientX,
                        y: event.clientY
                    }
                }
            };

            this.storageClient.saveEvent(this.convertToTrackingEvent(trackingEvent));
            this.sessionManager.updateActivity();
        } catch (error) {
            console.error('❌ Failed to track click:', error);
        }
    }

    /**
     * Setup scroll tracking
     */
    private setupScrollTracking(): void {
        if (!this.config.enableScrollTracking) return;

        const scrollHandler = () => {
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            this._scrollDepth = scrollHeight > 0 ? scrollTop / scrollHeight : 0;
            this._maxScrollDepth = Math.max(this._maxScrollDepth, this._scrollDepth);
        };

        window.addEventListener('scroll', scrollHandler, { passive: true });
        this.eventHandlers.push(() => window.removeEventListener('scroll', scrollHandler));
    }

    /**
     * Calculate page quality score
     */
    private calculateQualityScore(
        timeSpent: number,
        expectedTime: number,
        scrollDepth: number,
        interactions: number
    ): number {
        const timeRatio = Math.min(timeSpent / expectedTime, 2);
        const timeScore = Math.min(timeRatio * 40, 40);
        const scrollScore = Math.min(scrollDepth * 30, 30);
        const interactionScore = Math.min(interactions * 5, 30);

        return Math.min(timeScore + scrollScore + interactionScore, 100);
    }

    /**
     * Convert SimpleTrackingEvent to TrackingEvent format for storage
     */
    private convertToTrackingEvent(simpleEvent: SimpleTrackingEvent): any {
        return {
            project_id: this.config.projectId,
            session_id: simpleEvent.sessionId,
            user_id: simpleEvent.userId,
            event_type: simpleEvent.type,
            page_path: simpleEvent.metadata.path || this._currentPage || '/',
            metadata: simpleEvent.metadata,
            timestamp: simpleEvent.timestamp
        };
    }

    /**
     * Track custom event
     */
    async trackCustomEvent(eventType: string, metadata: Record<string, any>): Promise<void> {
        try {
            const session = this.sessionManager.currentSession;
            if (!session) return;

            const event: SimpleTrackingEvent = {
                id: crypto.randomUUID(),
                type: 'custom_event',
                timestamp: new Date().toISOString(),
                sessionId: session.sessionId,
                userId: session.userId,
                metadata: {
                    customEventType: eventType,
                    path: this._currentPage,
                    ...metadata
                }
            };

            await this.storageClient.saveEvent(this.convertToTrackingEvent(event));
            this.sessionManager.updateActivity();
        } catch (error) {
            console.error('❌ Failed to track custom event:', error);
        }
    }

    /**
     * Get current session info
     */
    getSessionInfo() {
        return this.sessionManager.currentSession;
    }

    /**
     * Get current page metrics
     */
    getCurrentPageMetrics() {
        return this.pageMetrics;
    }

    /**
     * Force flush all pending events
     */
    async flush(): Promise<void> {
        await this.storageClient.forceFlush();
    }

    /**
     * Clean shutdown
     */
    async destroy(): Promise<void> {
        try {
            // Track final page exit
            if (this._currentPage) {
                this.trackPageExit(this._currentPage);
            }

            // End session
            this.sessionManager.endSession();

            // Flush remaining events
            await this.storageClient.forceFlush();

            // Remove event listeners
            this.eventHandlers.forEach(cleanup => cleanup());

            this._isActive = false;

            if (this.config.debugMode) {
                console.log('🎯 User tracking destroyed');
            }
        } catch (error) {
            console.error('❌ Failed to destroy tracking:', error);
        }
    }
}

// Singleton instance for easy access
let trackerInstance: UserTracker | null = null;

/**
 * Get the global tracker instance
 */
export function getTracker(config?: Partial<TrackingConfig>): UserTracker {
    if (!trackerInstance && browser) {
        trackerInstance = new UserTracker(config);
    }
    return trackerInstance!;
}

/**
 * Initialize tracking with custom config
 */
export function initializeTracking(config?: Partial<TrackingConfig>): UserTracker {
    if (trackerInstance) {
        console.warn('⚠️ Tracking already initialized');
        return trackerInstance;
    }

    trackerInstance = new UserTracker(config);
    return trackerInstance;
}