// Types para o sistema de tracking

export interface TrackingEvent {
    id?: string;
    project_id: string;
    session_id: string;
    user_id?: string;
    event_type: EventType;
    page_path: string;
    metadata: EventMetadata;
    duration?: number;
    timestamp: string;
    created?: string;
    updated?: string;
}

// Simplified event structure for internal use
export interface SimpleTrackingEvent {
    id: string;
    type: string;
    timestamp: string;
    sessionId: string;
    userId?: string;
    metadata: Record<string, any>;
}

export type EventType =
    | 'session_start'
    | 'session_end'
    | 'page_enter'
    | 'page_exit'
    | 'page_threshold_reached'
    | 'button_click'
    | 'link_click'
    | 'form_start'
    | 'form_submit'
    | 'form_error'
    | 'navigation_flow'
    | 'scroll_milestone'
    | 'custom_event'
    // Error tracking events 🔥
    | 'error_caught'
    | 'error_component'
    | 'error_loading_timeout'
    | 'error_unexpected';

export interface EventMetadata {
    // Page tracking
    referrer?: string;
    user_agent?: string;
    screen_size?: string;
    viewport_size?: string;

    // Interaction tracking
    element_id?: string;
    element_class?: string;
    element_text?: string;
    button_type?: string;
    click_position?: { x: number; y: number };

    // Form tracking
    form_name?: string;
    field_name?: string;
    form_completion_rate?: number;
    validation_errors?: string[];

    // Navigation tracking
    from_page?: string;
    to_page?: string;
    navigation_type?: 'internal' | 'external' | 'refresh' | 'back';

    // Scroll tracking
    scroll_percentage?: number;
    max_scroll?: number;

    // Custom metadata
    [key: string]: any;
}

export interface PageThreshold {
    path: string;
    expectedTime: number;
    description?: string;
}

export interface TrackingConfig {
    projectId: string;
    trackingUrl: string;

    // Feature toggles
    enablePageTracking: boolean;
    enableInteractionTracking: boolean;
    enableFormTracking: boolean;
    enableScrollTracking: boolean;
    enableNavigationTracking: boolean;
    enableClickTracking: boolean;
    enableAutoTracking: boolean;

    // Batch processing
    batchSize: number;
    flushInterval: number;

    // Session management
    sessionTimeout: number;

    // Page thresholds
    pageThresholds: Record<string, number> & { default: number };

    // Debug
    debugMode: boolean;
}

export interface SessionInfo {
    sessionId: string;
    userId?: string;
    startTime: number;
    lastActivity: number;
    pageViews: number;
    isActive: boolean;
}

export interface PageMetrics {
    page: string;
    views: number;
    averageTime: number;
    bounceRate: number;
    conversionRate: number;
}

export interface InteractionEvent {
    type: 'click' | 'hover' | 'focus' | 'scroll';
    element: string;
    timestamp: number;
    position?: { x: number; y: number };
}

export interface FormEvent {
    type: 'start' | 'submit' | 'error' | 'abandon';
    formId: string;
    fields: string[];
    timestamp: number;
    completionRate?: number;
}

export interface NavigationEvent {
    type: 'navigate' | 'reload' | 'back' | 'forward';
    fromPath: string;
    toPath: string;
    timestamp: number;
    method: string;
}

export interface TrackingStats {
    totalEvents: number;
    sessionsToday: number;
    averageSessionTime: number;
    topPages: Array<{ page: string; views: number }>;
    conversionRate: number;
}

// 🔥 ERROR TRACKING INTERFACES - MODO PICA DAS GALAXIAS! 🔥

export interface ErrorConfig {
    discordWebhookUrl?: string;
    enableDiscordLogging: boolean;
    enableConsoleLogging: boolean;
    loadingTimeoutThreshold: number; // ms
    retryAttempts: number;
    environment: 'development' | 'staging' | 'production';
    projectName: string;
}

export interface ErrorEvent {
    id: string;
    type: 'caught' | 'component' | 'loading_timeout' | 'unexpected';
    timestamp: string;
    error: {
        name: string;
        message: string;
        stack?: string;
        code?: string | number;
    };
    context: {
        page: string;
        userAgent: string;
        url: string;
        userId?: string;
        sessionId?: string;
        // 🔥 INFORMAÇÕES DE ORIGEM - RASTREAMENTO DE FUNÇÕES!
        callerFunction?: string;
        fileName?: string;
        lineNumber?: string;
        fullStackTrace?: string;
        additionalData?: Record<string, any>;
    };
    severity: 'low' | 'medium' | 'high' | 'critical';
    tags: string[];
}

export interface LoadingTimeoutOptions {
    timeout: number;
    errorMessage?: string;
    context?: Record<string, any>;
    retryable?: boolean;
}