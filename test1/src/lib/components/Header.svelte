<script lang="ts">
  import { authStore } from '$lib/stores/auth.store.svelte';
  import { Button } from '$lib/components/ui/button';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Avatar from '$lib/components/ui/avatar';
  import Icon from '$lib/components/ui/icon.svelte';
</script>

<header class="border-b">
  <nav class="container mx-auto px-4 h-16 flex items-center justify-between">
    <a href="/" class="text-xl font-bold flex items-center gap-2">
      <Icon icon="mdi:rocket-launch" />
      MeuApp
    </a>
    
    <div class="flex items-center gap-4">
      {#if authStore.user}
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="ghost" size="icon" class="relative">
              <Avatar.Root class="h-8 w-8">
                <Avatar.Image src={null} alt={authStore.user.email} />
                <Avatar.Fallback>
                  {authStore.user.email.charAt(0).toUpperCase()}
                </Avatar.Fallback>
              </Avatar.Root>
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="end">
            <DropdownMenu.Label>{authStore.user.email}</DropdownMenu.Label>
            <DropdownMenu.Separator />
            <DropdownMenu.Item>
              <Icon icon="mdi:account" class="mr-2" size="16" />
              Perfil
            </DropdownMenu.Item>
            <DropdownMenu.Item>
              <Icon icon="mdi:cog" class="mr-2" size="16" />
              Configurações
            </DropdownMenu.Item>
            <DropdownMenu.Separator />
            <DropdownMenu.Item onclick={() => authStore.logout()}>
              <Icon icon="mdi:logout" class="mr-2" size="16" />
              Sair
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      {:else}
        <div class="flex items-center gap-2">
          <Button variant="ghost" size="sm" href="/auth/login">
            Login
          </Button>
          <Button size="sm" href="/auth/register">
            Criar Conta
          </Button>
        </div>
      {/if}
    </div>
  </nav>
</header>