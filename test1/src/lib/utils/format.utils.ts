// Utilitários para formatação de dados

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

export function formatCPF(cpf: string): string {
  // Remove tudo que não é número
  const cleaned = cpf.replace(/\D/g, '');
  
  // Aplica a máscara
  return cleaned.replace(
    /(\d{3})(\d{3})(\d{3})(\d{2})/,
    '$1.$2.$3-$4'
  );
}

export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 11) {
    return cleaned.replace(
      /(\d{2})(\d{5})(\d{4})/,
      '($1) $2-$3'
    );
  }
  
  return cleaned.replace(
    /(\d{2})(\d{4})(\d{4})/,
    '($1) $2-$3'
  );
}

export function cleanCPF(cpf: string): string {
  return cpf.replace(/\D/g, '');
}

export function cleanPhone(phone: string): string {
  return phone.replace(/\D/g, '');
}

export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}