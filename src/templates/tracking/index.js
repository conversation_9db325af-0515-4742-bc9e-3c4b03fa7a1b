// ========== TRACKING SYSTEM TEMPLATES ==========
// Importa todos os arquivos do sistema de tracking

import { trackingMainIndex } from './core/tracking-main-index.js';
import { trackingTypes } from './types/tracking-types.js';
import { trackerCore } from './core/tracker-core.js';
import { storageClient } from './core/storage-client.js';
import { sessionManager } from './core/session-manager.js';
import { eventFactory } from './utils/event-factory.js';
import { pageThresholds } from './config/page-thresholds.js';
import { environments } from './config/environments.js';
import { trackingReadme } from './docs/tracking-readme.js';
import { trackingChangelog } from './docs/tracking-changelog.js';
import { errorTracker } from './errors/error-tracker.js';
import { svelteHelpers } from './errors/svelte-helpers.js';
import { errorReadme } from './errors/error-readme.js';

// Exporta todos os templates do tracking system
export const trackingTemplates = {
  // Arquivo principal (src/lib/tracking/index.ts)
  trackingMainIndex,
  
  // Core files
  trackerCore,
  storageClient,
  sessionManager,
  
  // Types
  trackingTypes,
  
  // Utils
  eventFactory,
  
  // Config
  pageThresholds,
  environments,
  
  // Documentation
  trackingReadme,
  trackingChangelog,

  // Error tracking 🔥
  errorTracker,
  svelteHelpers,
  errorReadme
};

// Estrutura de arquivos que será criada
export const trackingFileStructure = {
  'src/lib/tracking': {
    'index.ts': trackingMainIndex,
    'README.md': trackingReadme,
    'CHANGELOG.md': trackingChangelog,
    'types': {
      'tracking.types.ts': trackingTypes
    },
    'core': {
      'tracker.svelte.ts': trackerCore,
      'storage-client.ts': storageClient,
      'session-manager.ts': sessionManager
    },
    'utils': {
      'event-factory.ts': eventFactory
    },
    'config': {
      'page-thresholds.ts': pageThresholds,
      'environments.ts': environments
    },
    'errors': {
      'error-tracker.ts': errorTracker,
      'svelte-helpers.ts': svelteHelpers,
      'README.md': errorReadme
    }
  }
};
