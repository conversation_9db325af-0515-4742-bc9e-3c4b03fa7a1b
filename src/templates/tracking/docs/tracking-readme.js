export const trackingReadme = `# 🎯 User Tracking System

Um sistema completo de tracking de usuários para SvelteKit com PocketBase, focado em simplicidade e métricas de qualidade.

## 📋 Índice

- [Instalação e Setup](#instalação-e-setup)
- [Uso Básico](#uso-básico)
- [Configuração](#configuração)
- [Presets Disponíveis](#presets-disponíveis)
- [Funções Helper](#funções-helper)
- [Exemplos Práticos](#exemplos-práticos)
- [Error Tracking](#error-tracking)
- [Métricas Coletadas](#métricas-coletadas)
- [TypeScript](#typescript)

## 🚀 Instalação e Setup

### 1. Inicialização no Layout Principal

No seu \`src/routes/+layout.svelte\`:

\`\`\`typescript
<script>
  import { onMount } from 'svelte';
  import { setupTracking } from '$lib/tracking';

  onMount(() => {
    // Setup básico (desenvolvimento)
    setupTracking({
      projectId: 'meu-projeto',
      trackingUrl: 'http://localhost:8090'
    });
    
    // OU usando presets
    // setupTrackingForDev('meu-projeto');
  });
</script>
\`\`\`

### 2. PocketBase Setup

Certifique-se de que seu PocketBase está rodando na URL configurada (\`http://localhost:8090\` por padrão).

## 📖 Uso Básico

### Tracking Automático

O sistema automaticamente rastreia:
- ✅ **Navegação entre páginas**
- ✅ **Tempo gasto em cada página**
- ✅ **Profundidade de scroll**
- ✅ **Cliques em elementos**
- ✅ **Interações com formulários**
- ✅ **Error tracking automático** (3 tipos de erros)

### Tracking Manual

Em qualquer componente \`.svelte\`:

\`\`\`typescript
<script>
  import { trackEvent, trackButtonClick, getCurrentTracker } from '$lib/tracking';

  function handleClick() {
    // Jeito simples
    trackButtonClick('save-button', 'Save Document');
    
    // OU jeito mais detalhado
    trackEvent('user_action', {
      action: 'save_document',
      documentType: 'blog_post',
      wordCount: 1500
    });
  }

  function handleFeatureUsage() {
    // Acessar o tracker diretamente
    const tracker = getCurrentTracker();
    if (tracker) {
      tracker.trackCustomEvent('feature_usage', {
        feature: 'dark_mode_toggle',
        enabled: true
      });
    }
  }
</script>

<button on:click={handleClick}>Save</button>
<button on:click={handleFeatureUsage}>Toggle Dark Mode</button>
\`\`\`

## ⚙️ Configuração

### Configuração Completa

\`\`\`typescript
import { setupTracking } from '$lib/tracking';

setupTracking({
  // OBRIGATÓRIOS
  projectId: 'meu-projeto',
  trackingUrl: 'https://meu-pocketbase.com',
  
  // OPCIONAIS - Funcionalidades
  enablePageTracking: true,
  enableInteractionTracking: true,
  enableFormTracking: true,
  enableScrollTracking: true,
  enableNavigationTracking: true,
  enableClickTracking: true,
  enableAutoTracking: true,
  debugMode: false,
  
  // OPCIONAIS - Performance
  batchSize: 10,                    // Eventos por lote
  flushInterval: 30000,             // 30 segundos
  sessionTimeout: 30 * 60 * 1000,   // 30 minutos
  
  // OPCIONAIS - Thresholds de qualidade por página
  pageThresholds: {
    '/': 60000,           // Home - 1 minuto
    '/dashboard': 120000, // Dashboard - 2 minutos
    '/docs': 300000,      // Docs - 5 minutos
    default: 45000        // Padrão - 45 segundos
  }
});
\`\`\`

## 🎨 Presets Disponíveis

### 1. Minimal (Básico)
\`\`\`typescript
import { setupTracking, TrackingPresets } from '$lib/tracking';

setupTracking({
  projectId: 'meu-projeto',
  trackingUrl: 'http://localhost:8090',
  ...TrackingPresets.minimal()
});
\`\`\`
- ✅ Páginas visitadas
- ✅ Navegação
- ❌ Interações detalhadas

### 2. Standard (Recomendado)
\`\`\`typescript
setupTracking({
  projectId: 'meu-projeto',
  trackingUrl: 'http://localhost:8090',
  ...TrackingPresets.standard()
});
\`\`\`
- ✅ Todas as funcionalidades
- ✅ Performance balanceada
- ✅ Ideal para produção

### 3. Development (Debug)
\`\`\`typescript
import { setupTrackingForDev } from '$lib/tracking';

setupTrackingForDev('meu-projeto');
\`\`\`
- ✅ Envio imediato (debug)
- ✅ Logs detalhados
- ✅ Sessions curtas

### 4. Privacy (Privacidade)
\`\`\`typescript
setupTracking({
  projectId: 'meu-projeto',
  trackingUrl: 'http://localhost:8090',
  ...TrackingPresets.privacy()
});
\`\`\`
- ✅ Apenas navegação básica
- ❌ Sem tracking de formulários
- ❌ Sem interações detalhadas

## 🛠 Funções Helper

### Tracking de Eventos
\`\`\`typescript
import { 
  trackEvent, 
  trackPageView, 
  trackButtonClick, 
  trackFeatureUsage 
} from '$lib/tracking';

// Evento personalizado
trackEvent('user_signup', {
  plan: 'premium',
  source: 'landing_page'
});

// View de página manual
trackPageView('modal_opened', {
  modalType: 'settings'
});

// Click de botão
trackButtonClick('checkout', 'Proceed to Checkout', {
  cartValue: 99.99,
  itemCount: 3
});

// Uso de feature
trackFeatureUsage('search', 'filter_applied', {
  filterType: 'price',
  resultCount: 42
});
\`\`\`

### Setup para Diferentes Ambientes

\`\`\`typescript
// Desenvolvimento
import { setupTrackingForDev } from '$lib/tracking';
setupTrackingForDev('meu-projeto');

// Produção
import { setupTrackingForProd } from '$lib/tracking';
setupTrackingForProd('meu-projeto', 'https://tracking.meusite.com');

// Privacidade
import { setupPrivacyTracking } from '$lib/tracking';
setupPrivacyTracking('meu-projeto', 'https://tracking.meusite.com');
\`\`\`

## 🚨 Error Tracking

O sistema inclui um módulo completo de error tracking com 3 tipos de captura:

### 1. 🕸️ Try/Catch Errors
\`\`\`typescript
import { safeCall } from '$lib/tracking/errors/svelte-helpers';

const result = await safeCall(
  () => fetch('/api/data').then(r => r.json()),
  'fetch_data'
);
\`\`\`

### 2. 🎨 Component Errors
\`\`\`svelte
<script>
  import { errorBoundary } from '$lib/tracking/errors/svelte-helpers';
</script>

<div use:errorBoundary={{ componentName: 'UserProfile' }}>
  <UserComponent />
</div>
\`\`\`

### 3. ⏱️ Loading Timeout
\`\`\`typescript
import { withTimeout } from '$lib/tracking/errors/svelte-helpers';

const data = await withTimeout(
  () => heavyOperation(),
  5000, // 5 segundos
  'Operação demorou muito'
);
\`\`\`

### Setup com Discord (Opcional)
\`\`\`typescript
import { setupGlobalErrorTracking } from '$lib/tracking/errors/svelte-helpers';

setupGlobalErrorTracking(
  'https://discord.com/api/webhooks/YOUR_WEBHOOK_URL',
  'Meu App',
  'production'
);
\`\`\`

### Exemplos Completos
- **Demo completa**: \`/examples/error-demo\`
- **Teste de origem**: \`/examples/error-origin-test\`
- **Documentação**: \`src/lib/tracking/errors/README.md\`

## 📊 Métricas Coletadas

### Automáticas
- **Páginas visitadas** - URL, tempo de permanência, referrer
- **Navegação** - Fluxo entre páginas, profundidade
- **Scroll depth** - Porcentagem de scroll máxima
- **Interações** - Cliques, teclas pressionadas
- **Formulários** - Campos preenchidos, submissions, erros
- **Performance** - Tempo de carregamento, métricas de UX

### Quality Score
Cada página recebe um score de qualidade (0-100) baseado em:
- **Tempo esperado vs real** (40 pontos)
- **Profundidade de scroll** (30 pontos)  
- **Número de interações** (30 pontos)

### Sessions
- **ID único** por sessão
- **Duração** da sessão
- **Número de páginas** visitadas
- **Tempo total** de atividade

## 🔧 TypeScript

O sistema é totalmente tipado. Principais tipos:

\`\`\`typescript
import type { 
  TrackingConfig,
  TrackingEvent,
  PageMetrics,
  SessionInfo 
} from '$lib/tracking';

// Configuração customizada
const config: Partial<TrackingConfig> = {
  projectId: 'meu-projeto',
  debugMode: true
};

// Acessar métricas
const tracker = getCurrentTracker();
const metrics: PageMetrics | null = tracker?.getCurrentPageMetrics();
const session: SessionInfo = tracker?.getSessionInfo();
\`\`\`

## 🎯 Próximos Passos

1. **Implemente** no seu \`+layout.svelte\`
2. **Configure** seu projeto no PocketBase
3. **Teste** os exemplos em \`/examples/tracking\` e \`/examples/error-demo\`
4. **Adicione tracking manual** onde necessário
5. **Monitore** os dados coletados
6. **Ajuste** os thresholds baseado no comportamento real

## 💡 Dicas

- Use **presets** para começar rapidamente
- Configure **pageThresholds** baseado no seu conteúdo
- Use **debugMode: true** durante desenvolvimento
- **Respeite a privacidade** - use preset \`privacy\` quando necessário
- **Monitore performance** - ajuste \`batchSize\` e \`flushInterval\`

---

✨ **Sistema pronto para uso!** Comece com \`setupTrackingForDev()\` e evolua conforme sua necessidade.`;
