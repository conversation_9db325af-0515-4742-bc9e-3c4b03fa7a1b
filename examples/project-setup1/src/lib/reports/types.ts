// types.ts
export enum ErrorType {
    HTTP_ERROR = 'HTTP_ERROR',
    COMPONENT_ERROR = 'COMPONENT_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    FATAL_ERROR = 'FATAL_ERROR'
}

export interface ErrorContext {
    route: string;
    timestamp: string;
    userAgent: string;
    // outros contextos que possam surgir
}

export interface ErrorDetails {
    functionName?: string;
    message: string;
    statusCode?: number; // para HTTP_ERROR
    originalError?: Error;
}

export interface ReportableError {
    type: ErrorType;
    context: ErrorContext;
    details: ErrorDetails;
}