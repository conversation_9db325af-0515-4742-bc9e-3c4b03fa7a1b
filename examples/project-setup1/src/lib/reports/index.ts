// index.ts

import { <PERSON>rror<PERSON>eporter } from './reporters.js';
import { ErrorType } from './types.js';
import type { ErrorDetails } from './types.js';

// Re-exports for external use
export { ErrorType } from './types.js';
export type { ErrorContext, ErrorDetails, ReportableError } from './types.js';
export { ErrorReporter } from './reporters.js';

// Global error reporter instance
let globalReporter: ErrorReporter | null = null;

/**
 * Initializes the global error reporting system
 * Sets up fatal error handlers and creates singleton reporter
 * Should be called once in your root layout
 * 
 * @param webhookUrl - Discord webhook URL (optional, will use env var)
 * @param enableInDev - Enable reporting in development mode
 * 
 * @example
 * ```typescript
 * // In your +layout.svelte
 * import { initErrorReporting } from '$lib/error-reporting';
 * import { onMount } from 'svelte';
 * 
 * onMount(() => {
 *   initErrorReporting();
 * });
 * ```
 */
export function initErrorReporting(webhookUrl?: string, enableInDev: boolean = false): void {
    // Prevent multiple initializations
    if (globalReporter) {
        console.warn('Error reporting already initialized');
        return;
    }

    // Create global reporter instance
    globalReporter = new ErrorReporter(webhookUrl);

    // Set up fatal error handlers
    setupFatalErrorHandlers(enableInDev);
}

/**
 * Gets the global error reporter instance
 * Must be called after initErrorReporting()
 * 
 * @returns Global ErrorReporter instance
 * @throws Error if not initialized
 * 
 * @example
 * ```typescript
 * const reporter = getErrorReporter();
 * await reporter.reportError('HTTP_ERROR', {
 *   functionName: 'fetchData',
 *   message: 'API call failed'
 * });
 * ```
 */
export function getErrorReporter(): ErrorReporter {
    if (!globalReporter) {
        throw new Error('Error reporting not initialized. Call initErrorReporting() first.');
    }
    return globalReporter;
}

/**
 * Quick report function for convenience
 * Uses the global reporter instance
 * 
 * @param type - Error type
 * @param details - Error details
 * @param force - Force reporting in dev mode
 * 
 * @example
 * ```typescript
 * import { reportError } from '$lib/error-reporting';
 * 
 * try {
 *   await fetchData();
 * } catch (error) {
 *   await reportError('HTTP_ERROR', {
 *     functionName: 'fetchData',
 *     message: error.message,
 *     statusCode: error.status
 *   });
 * }
 * ```
 */
export async function reportError(
    type: ErrorType,
    details: ErrorDetails,
    force: boolean = false
): Promise<void> {
    try {
        const reporter = getErrorReporter();
        await reporter.reportError(type, details, force);
    } catch (error) {
        console.warn('Quick report failed:', error);
    }
}

/**
 * Sets up global error handlers for fatal errors
 * Captures unhandled errors and promise rejections
 */
function setupFatalErrorHandlers(enableInDev: boolean): void {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
        if (globalReporter) {
            globalReporter.reportError(ErrorType.FATAL_ERROR, {
                functionName: 'globalErrorHandler',
                message: `${event.error?.message || event.message} at ${event.filename}:${event.lineno}:${event.colno}`,
                originalError: event.error
            }, enableInDev);
        }
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        if (globalReporter) {
            globalReporter.reportError(ErrorType.FATAL_ERROR, {
                functionName: 'unhandledPromiseRejection',
                message: `Unhandled Promise Rejection: ${event.reason?.message || event.reason}`,
                originalError: event.reason instanceof Error ? event.reason : new Error(String(event.reason))
            }, enableInDev);
        }
    });
}