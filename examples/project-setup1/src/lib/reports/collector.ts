import type { ErrorContext } from './types.js';
import { browser } from '$app/environment';

/**
 * <PERSON>les collection of error context information
 * Ensures browser-only execution to avoid SSR issues
 */
export class ContextCollector {
    /**
     * Gets the current route using native JavaScript
     * @returns Current route path with query params
     */
    private getCurrentRoute(): string {
        if (!browser || typeof window === 'undefined') {
            return 'ssr-context';
        }

        return window.location.pathname + window.location.search;
    }

    /**
     * Generates ISO timestamp for error occurrence
     * @returns ISO formatted timestamp
     */
    private getTimestamp(): string {
        return new Date().toISOString();
    }

    /**
     * Extracts browser information from user agent
     * @returns Browser name and version
     */
    private getBrowserInfo(): string {
        if (!browser || typeof navigator === 'undefined') {
            return 'server-context';
        }

        const ua = navigator.userAgent;

        // Simple browser detection
        if (ua.includes('Chrome/')) {
            const version = ua.match(/Chrome\/([0-9.]+)/)?.[1];
            return `Chrome ${version}`;
        }
        if (ua.includes('Firefox/')) {
            const version = ua.match(/Firefox\/([0-9.]+)/)?.[1];
            return `Firefox ${version}`;
        }
        if (ua.includes('Safari/') && !ua.includes('Chrome/')) {
            const version = ua.match(/Version\/([0-9.]+)/)?.[1];
            return `Safari ${version}`;
        }

        return 'unknown-browser';
    }

    /**
     * Collects all context information for error reporting
     * Safe for SSR - won't break on server-side execution
     * @returns Complete error context
     */
    public collectContext(): ErrorContext {
        return {
            route: this.getCurrentRoute(),
            timestamp: this.getTimestamp(),
            userAgent: this.getBrowserInfo()
        };
    }
}