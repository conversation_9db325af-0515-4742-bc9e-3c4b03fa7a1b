# Error Reporting System Documentation

A clean and focused error reporting system for SvelteKit applications that sends errors to Discord via webhooks.

## 🚀 Quick Start

### 1. Environment Setup
Create a Discord webhook and add it to your `.env` file:
```bash
VITE_DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-url
```

### 2. Initialize in Root Layout
```svelte
<!-- src/routes/+layout.svelte -->
<script>
  import { onMount } from 'svelte';
  import { initErrorReporting } from '$lib/error-reporting';

  onMount(() => {
    initErrorReporting();
    
    // For testing in development (optional)
    // initErrorReporting(undefined, true);
  });
</script>

<slot />
```

### 3. Start Reporting Errors
```typescript
import { reportError } from '$lib/error-reporting';

// Simple error report
await reportError('HTTP_ERROR', {
  functionName: 'fetchUserData',
  message: 'Failed to load user profile',
  statusCode: 404
});
```

## 📋 Error Types

The system supports four main error types:

- **`HTTP_ERROR`** 🔴 - API calls, network requests
- **`COMPONENT_ERROR`** 🟡 - Svelte component errors  
- **`TIMEOUT_ERROR`** 🟠 - Operations that exceed time limits
- **`FATAL_ERROR`** 💀 - Unhandled errors that crash the app

## 📖 Usage Examples

### HTTP Error Reporting

#### Basic HTTP Wrapper
```typescript
// src/lib/api.ts
import { reportError } from '$lib/error-reporting';

export async function apiCall(url: string, options?: RequestInit) {
  try {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      await reportError('HTTP_ERROR', {
        functionName: 'apiCall',
        message: `HTTP ${response.status}: ${response.statusText} for ${url}`,
        statusCode: response.status
      });
      
      throw new Error(`API call failed: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    await reportError('HTTP_ERROR', {
      functionName: 'apiCall',
      message: `Network error: ${error.message}`,
      originalError: error
    });
    
    throw error;
  }
}
```

#### Usage in Load Functions
```typescript
// src/routes/dashboard/+page.ts
import { apiCall } from '$lib/api';

export async function load() {
  try {
    const data = await apiCall('/api/dashboard');
    return { dashboard: data };
  } catch (error) {
    // Error already reported by apiCall wrapper
    return { dashboard: null, error: error.message };
  }
}
```

#### Usage in Components
```svelte
<!-- src/routes/profile/+page.svelte -->
<script>
  import { apiCall } from '$lib/api';
  
  let userData = null;
  let loading = true;
  
  async function loadProfile() {
    try {
      userData = await apiCall('/api/user/profile');
    } catch (error) {
      console.error('Failed to load profile:', error);
    } finally {
      loading = false;
    }
  }
  
  onMount(loadProfile);
</script>

{#if loading}
  <p>Loading...</p>
{:else if userData}
  <h1>Welcome, {userData.name}</h1>
{:else}
  <p>Failed to load profile</p>
{/if}
```

### Component Error Reporting

#### Using Error Boundary Action
```svelte
<!-- src/lib/components/UserCard.svelte -->
<script>
  import { errorBoundary } from '$lib/error-reporting/errorBoundaryReport';
  
  export let user;
  
  // This function might throw an error
  function formatUserName(user) {
    return user.firstName + ' ' + user.lastName; // Error if user is null
  }
</script>

<!-- Error boundary wraps the component -->
<div use:errorBoundary={{ componentName: 'UserCard' }}>
  <div class="card">
    <h3>{formatUserName(user)}</h3>
    <p>{user.email}</p>
  </div>
</div>

<style>
  .card { padding: 1rem; border: 1px solid #ccc; }
</style>
```

#### Advanced Error Boundary with Custom Handling
```svelte
<!-- src/routes/dashboard/+page.svelte -->
<script>
  import { errorBoundary } from '$lib/error-reporting/errorBoundaryReport';
  
  let hasError = false;
  
  function handleComponentError(error, element) {
    hasError = true;
    element.innerHTML = '<p>⚠️ Something went wrong</p>';
  }
</script>

<div use:errorBoundary={{ 
  componentName: 'DashboardWidgets',
  onError: handleComponentError,
  enableInDev: true 
}}>
  {#if !hasError}
    <!-- Complex dashboard widgets that might error -->
    <DashboardChart />
    <UserStats />
    <RecentActivity />
  {/if}
</div>
```

#### Development Error Boundary
```svelte
<!-- For development/testing -->
<script>
  import { devErrorBoundary } from '$lib/error-reporting/errorBoundaryReport';
</script>

<!-- Shows visual feedback and detailed logging in dev -->
<div use:devErrorBoundary={{ componentName: 'TestComponent' }}>
  <button on:click={() => { throw new Error('Test error') }}>
    Trigger Error (Dev Only)
  </button>
</div>
```

### Timeout Error Reporting

#### Timeout Wrapper Function
```typescript
// src/lib/timeout-wrapper.ts
import { reportError } from '$lib/error-reporting';

export async function withTimeout<T>(
  promise: Promise<T>, 
  timeoutMs: number, 
  functionName: string
): Promise<T> {
  const controller = new AbortController();
  
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      controller.abort();
      
      reportError('TIMEOUT_ERROR', {
        functionName,
        message: `Operation timed out after ${timeoutMs}ms`
      });
      
      reject(new Error(`Timeout after ${timeoutMs}ms`));
    }, timeoutMs);
  });
  
  try {
    return await Promise.race([promise, timeoutPromise]);
  } finally {
    controller.abort();
  }
}
```

#### Using Timeout Wrapper
```typescript
// src/routes/reports/+page.ts
import { apiCall } from '$lib/api';
import { withTimeout } from '$lib/timeout-wrapper';

export async function load() {
  try {
    // Large report with 30-second timeout
    const reportData = await withTimeout(
      apiCall('/api/reports/large-dataset'), 
      30000, 
      'loadLargeReport'
    );
    
    return { report: reportData };
  } catch (error) {
    if (error.message.includes('Timeout')) {
      // Timeout error already reported
      return { report: null, timedOut: true };
    }
    throw error;
  }
}
```

### Manual Error Reporting

#### Critical Operations
```typescript
// src/lib/critical-operations.ts
import { reportError, getErrorReporter } from '$lib/error-reporting';

export async function saveUserPreferences(preferences: any) {
  try {
    const result = await apiCall('/api/preferences', {
      method: 'POST',
      body: JSON.stringify(preferences)
    });
    
    return result;
  } catch (error) {
    // Critical operation - always report
    await reportError('HTTP_ERROR', {
      functionName: 'saveUserPreferences',
      message: `CRITICAL: Failed to save user preferences: ${error.message}`,
      statusCode: error.status || 0
    });
    
    throw error;
  }
}
```

#### Using Reporter Instance Directly
```typescript
// src/lib/complex-operations.ts
import { getErrorReporter } from '$lib/error-reporting';

export async function complexDataProcessing(data: any[]) {
  const reporter = getErrorReporter();
  
  try {
    // Multi-step process
    const step1 = await processStep1(data);
    const step2 = await processStep2(step1);
    return await processStep3(step2);
    
  } catch (error) {
    // Detailed error reporting with context
    await reporter.reportError('COMPONENT_ERROR', {
      functionName: 'complexDataProcessing',
      message: `Data processing failed at step: ${error.step || 'unknown'}. Data size: ${data.length} items. Error: ${error.message}`,
      originalError: error
    });
    
    throw error;
  }
}
```

### Fatal Error Handling

Fatal errors are automatically captured when you initialize the system. They include:

- Unhandled JavaScript errors (`window.onerror`)
- Unhandled promise rejections (`window.onunhandledrejection`)

```typescript
// These are captured automatically - no code needed!

// This will be auto-reported as FATAL_ERROR:
setTimeout(() => {
  throw new Error('Uncaught error');
}, 1000);

// This will also be auto-reported as FATAL_ERROR:
Promise.reject(new Error('Unhandled promise rejection'));
```

## 🔧 API Reference

### Core Functions

#### `initErrorReporting(webhookUrl?, enableInDev?)`
Initializes the global error reporting system.

```typescript
initErrorReporting();                        // Use env var webhook
initErrorReporting('https://...', false);    // Custom webhook, prod only
initErrorReporting(undefined, true);         // Enable in dev mode
```

#### `reportError(type, details, force?)`
Reports an error using the global reporter instance.

```typescript
await reportError('HTTP_ERROR', {
  functionName: 'myFunction',
  message: 'Something went wrong',
  statusCode: 500,           // Optional: for HTTP errors
  originalError: error       // Optional: original error object
}, false);                   // Optional: force in dev mode
```

#### `getErrorReporter()`
Gets the global reporter instance for advanced usage.

```typescript
const reporter = getErrorReporter();
await reporter.reportError('COMPONENT_ERROR', details);
```

### Error Details Interface

```typescript
interface ErrorDetails {
  functionName?: string;    // Function where error occurred
  message: string;          // Error message (required)
  statusCode?: number;      // HTTP status code (for HTTP_ERROR)
  originalError?: Error;    // Original error object
}
```

### Error Boundary Action

#### `errorBoundary(node, options)`
Svelte action for automatic component error reporting.

```typescript
interface ErrorBoundaryOptions {
  componentName?: string;                           // Component name for reporting
  enableInDev?: boolean;                           // Report in dev mode
  onError?: (error: Error, element: HTMLElement) => void;  // Custom handler
  preventDefault?: boolean;                        // Prevent error bubbling
}
```

#### `createErrorBoundary(defaultOptions)`
Creates a pre-configured error boundary action.

```typescript
const myErrorBoundary = createErrorBoundary({
  enableInDev: true,
  preventDefault: true
});
```

#### `devErrorBoundary`
Pre-configured error boundary for development with visual feedback.

## 🎛️ Configuration

### Environment Variables
```bash
# Required: Discord webhook URL
VITE_DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-id/your-webhook-token
```

### Error Grouping
The system automatically groups similar errors within 5-minute windows to prevent spam. Errors are grouped by:
- Error type
- Function name  
- Route
- Error message

### Development vs Production
- **Production**: Only reports errors to Discord
- **Development**: Logs errors to console, optional Discord reporting with `force: true`

## 🎨 Discord Message Format

Errors appear in Discord as embedded messages with:

- **🔴 HTTP_ERROR** - Red embed
- **🟡 COMPONENT_ERROR** - Orange embed  
- **🟠 TIMEOUT_ERROR** - Dark orange embed
- **💀 FATAL_ERROR** - Dark red embed

Each message includes:
- 📍 Route where error occurred
- 🔧 Function name
- 🕐 Timestamp
- 🌐 Browser information
- 📊 HTTP status code (if applicable)
- 🔄 Occurrence count (if grouped)
- 💬 Error message

## 🧪 Testing

### Test Error Reporting in Development
```svelte
<!-- src/routes/test-errors/+page.svelte -->
<script>
  import { reportError } from '$lib/error-reporting';
  
  async function testHttpError() {
    await reportError('HTTP_ERROR', {
      functionName: 'testButton',
      message: 'Test HTTP error from development',
      statusCode: 500
    }, true); // force = true for dev testing
  }
  
  async function testComponentError() {
    await reportError('COMPONENT_ERROR', {
      functionName: 'TestComponent',
      message: 'Test component error with detailed context'
    }, true);
  }
  
  function testFatalError() {
    // This will be auto-captured by global handlers
    throw new Error('Test fatal error - auto-reported');
  }
</script>

<div>
  <h1>Error Reporting Tests</h1>
  <button on:click={testHttpError}>Test HTTP Error</button>
  <button on:click={testComponentError}>Test Component Error</button>
  <button on:click={testFatalError}>Test Fatal Error</button>
</div>
```

### Verify Setup
1. Add test buttons to a development page
2. Click buttons to trigger different error types
3. Check Discord channel for error messages
4. Verify console logging in development mode

## ❓ Troubleshooting

### Common Issues

**Error: "Error reporting not initialized"**
```typescript
// Make sure you call initErrorReporting() in your root layout
initErrorReporting();
```

**Webhook not working**
```bash
# Check your environment variable
echo $VITE_DISCORD_WEBHOOK_URL

# Verify the webhook URL is correct in Discord
```

**No errors appearing in Discord**
```typescript
// Test with force flag in development
await reportError('HTTP_ERROR', {
  functionName: 'test',
  message: 'test message'
}, true); // force = true
```

**Too many duplicate errors**
- The system groups similar errors within 5-minute windows
- Check if your error messages are too specific (include dynamic data)
- Consider using more generic error messages for grouping

### Debug Mode
Enable detailed logging by setting force mode:
```typescript
// This will log to console in dev mode
await reportError('HTTP_ERROR', details, true);
```

## 📁 File Structure

```
src/lib/error-reporting/
├── index.ts                    # Main exports and initialization
├── types.ts                    # Type definitions
├── collectors.ts               # Context collection (routes, browser info)
├── discord.ts                  # Discord webhook and error grouping
├── reporters.ts                # Main reporting logic
├── errorBoundaryReport.ts      # Component error boundary action
└── docs.md                     # This documentation
```

## 🚦 Best Practices

1. **Initialize once** in your root layout
2. **Use wrapper functions** for consistent HTTP error handling
3. **Add error boundaries** to critical components
4. **Include function names** for better error tracking
5. **Test in development** with force flags before production
6. **Don't over-report** - let the grouping system prevent spam
7. **Keep error messages descriptive** but not too specific for grouping