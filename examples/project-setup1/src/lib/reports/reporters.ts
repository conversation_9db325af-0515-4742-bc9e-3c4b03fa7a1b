// reporters.ts

import { ContextCollector } from './collector';
import { DiscordReporter } from './discord';
import type { ErrorType, ErrorDetails, ReportableError } from './types';

/**
 * Main error reporting system
 * Handles error collection, formatting, and Discord reporting
 * 
 * @example
 * ```typescript
 * const reporter = new ErrorReporter();
 * 
 * // Report HTTP error
 * await reporter.reportError('HTTP_ERROR', {
 *   functionName: 'fetchUserData',
 *   message: 'Failed to fetch user profile',
 *   statusCode: 404
 * });
 * 
 * // Report component error
 * await reporter.reportError('COMPONENT_ERROR', {
 *   functionName: 'UserProfileCard',
 *   message: 'Cannot read property "name" of undefined',
 *   originalError: error
 * });
 * ```
 */
export class ErrorReporter {
    private contextCollector: ContextCollector;
    private discordReporter: DiscordReporter;

    constructor(webhookUrl?: string) {
        this.contextCollector = new ContextCollector();
        this.discordReporter = new DiscordReporter(webhookUrl);
    }

    /**
     * Reports an error to Discord with full context
     * Automatically collects route, timestamp, and browser info
     * 
     * @param type - Type of error being reported
     * @param details - Error details including message and optional metadata
     * @param force - Force reporting in dev mode (useful for testing)
     * 
     * @example
     * ```typescript
     * // HTTP Error with status code
     * await reporter.reportError('HTTP_ERROR', {
     *   functionName: 'apiCall',
     *   message: 'Request failed with 500 error',
     *   statusCode: 500
     * });
     * 
     * // Component error with original error object
     * try {
     *   // component logic
     * } catch (error) {
     *   await reporter.reportError('COMPONENT_ERROR', {
     *     functionName: 'MyComponent',
     *     message: error.message,
     *     originalError: error
     *   });
     * }
     * 
     * // Timeout error
     * await reporter.reportError('TIMEOUT_ERROR', {
     *   functionName: 'longRunningTask',
     *   message: 'Operation exceeded 30 second timeout'
     * });
     * ```
     */
    public async reportError(
        type: ErrorType,
        details: ErrorDetails,
        force: boolean = false
    ): Promise<void> {
        try {
            // Validate required fields
            if (!type || !details?.message) {
                console.warn('Error reporting skipped: type and message are required');
                return;
            }

            // Collect current context
            const context = this.contextCollector.collectContext();

            // Create reportable error object
            const reportableError: ReportableError = {
                type,
                context,
                details: {
                    ...details,
                    // Ensure we have a function name for grouping
                    functionName: details.functionName || 'anonymous'
                }
            };

            // Send to Discord
            await this.discordReporter.reportError(reportableError, force);

        } catch (reportingError) {
            // Never let error reporting break the app
            console.warn('Error reporting system failed:', reportingError);
        }
    }

    /**
     * Reports multiple errors in batch
     * Useful for collecting errors and reporting them together
     * 
     * @param errors - Array of error objects to report
     * @param force - Force reporting in dev mode
     * 
     * @example
     * ```typescript
     * const errors = [
     *   { type: 'HTTP_ERROR', details: { message: 'API call failed', statusCode: 500 } },
     *   { type: 'COMPONENT_ERROR', details: { message: 'Render failed' } }
     * ];
     * 
     * await reporter.reportErrors(errors);
     * ```
     */
    public async reportErrors(
        errors: Array<{ type: ErrorType; details: ErrorDetails }>,
        force: boolean = false
    ): Promise<void> {
        const reportPromises = errors.map(error =>
            this.reportError(error.type, error.details, force)
        );

        try {
            await Promise.allSettled(reportPromises);
        } catch (batchError) {
            console.warn('Batch error reporting failed:', batchError);
        }
    }
}