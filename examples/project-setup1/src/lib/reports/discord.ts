// discord.ts

import { dev } from '$app/environment';

interface GroupedError {
    key: string;
    count: number;
    firstOccurrence: number;
    lastReport: number;
}

/**
 * Manages Discord webhook reporting and error grouping
 * Prevents spam by grouping similar errors within 5-minute windows
 */
export class DiscordReporter {
    private readonly webhookUrl: string;
    private readonly groupingWindow = 5 * 60 * 1000; // 5 minutes in ms
    private errorGroups = new Map<string, GroupedError>();

    constructor(webhookUrl?: string) {
        this.webhookUrl = webhookUrl || import.meta.env.VITE_DISCORD_WEBHOOK_URL || '';
    }

    /**
     * Creates a unique key for error grouping
     * Groups by: type + function + route + message
     */
    private createErrorKey(type: string, functionName: string, route: string, message: string): string {
        return `${type}:${functionName}:${route}:${message}`;
    }

    /**
     * Checks if error should be reported based on grouping rules
     */
    private shouldReport(errorKey: string): boolean {
        const now = Date.now();
        const existing = this.errorGroups.get(errorKey);

        if (!existing) {
            // First occurrence - always report
            this.errorGroups.set(errorKey, {
                key: errorKey,
                count: 1,
                firstOccurrence: now,
                lastReport: now
            });
            return true;
        }

        // Check if grouping window has passed
        if (now - existing.lastReport > this.groupingWindow) {
            // Update last report time and increment count
            existing.lastReport = now;
            existing.count += 1;
            return true;
        }

        // Within grouping window - don't report
        existing.count += 1;
        return false;
    }

    /**
     * Gets emoji and color for error type
     */
    private getErrorStyle(type: string): { emoji: string; color: number } {
        const styles = {
            HTTP_ERROR: { emoji: '🔴', color: 0xff0000 },      // Red
            COMPONENT_ERROR: { emoji: '🟡', color: 0xffa500 }, // Orange  
            TIMEOUT_ERROR: { emoji: '🟠', color: 0xff8c00 },   // Dark Orange
            FATAL_ERROR: { emoji: '💀', color: 0x8b0000 }      // Dark Red
        };

        return styles[type as keyof typeof styles] || { emoji: '⚪', color: 0x808080 };
    }

    /**
     * Formats error for Discord embed
     */
    private formatErrorEmbed(error: any): any {
        const style = this.getErrorStyle(error.type);
        const grouped = this.errorGroups.get(
            this.createErrorKey(error.type, error.details.functionName || 'unknown', error.context.route, error.details.message)
        );

        return {
            embeds: [{
                title: `${style.emoji} ${error.type}`,
                color: style.color,
                fields: [
                    {
                        name: '📍 Route',
                        value: error.context.route,
                        inline: true
                    },
                    {
                        name: '🔧 Function',
                        value: error.details.functionName || 'unknown',
                        inline: true
                    },
                    {
                        name: '🕐 Time',
                        value: error.context.timestamp,
                        inline: true
                    },
                    {
                        name: '🌐 Browser',
                        value: error.context.userAgent,
                        inline: true
                    },
                    ...(error.details.statusCode ? [{
                        name: '📊 Status Code',
                        value: error.details.statusCode.toString(),
                        inline: true
                    }] : []),
                    ...(grouped && grouped.count > 1 ? [{
                        name: '🔄 Occurrences',
                        value: `${grouped.count} times`,
                        inline: true
                    }] : []),
                    {
                        name: '💬 Message',
                        value: '```\n' + error.details.message + '\n```',
                        inline: false
                    }
                ],
                timestamp: error.context.timestamp
            }]
        };
    }

    /**
     * Sends error report to Discord
     * Only reports in production unless force flag is set
     * @param error - The error to report
     * @param force - Force reporting in dev mode (default: false)
     */
    public async reportError(error: any, force: boolean = false): Promise<void> {
        // Environment check
        if (dev && !force) {
            console.log('🔍 [Error Reporter - DEV]', error);
            return;
        }

        if (!this.webhookUrl) {
            if (dev) console.error('Discord webhook URL not configured');
            return;
        }

        // Check if should report based on grouping
        const errorKey = this.createErrorKey(
            error.type,
            error.details.functionName || 'unknown',
            error.context.route,
            error.details.message
        );

        if (!this.shouldReport(errorKey)) {
            return; // Skip reporting due to grouping
        }

        try {
            const payload = this.formatErrorEmbed(error);

            const response = await fetch(this.webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`Discord webhook failed: ${response.status}`);
            }

        } catch (webhookError) {
            // Only log webhook failures in dev
            if (dev) {
                console.error('Failed to send error to Discord:', webhookError);
            }
        }
    }
}