// Gerenciador de sessões de usuário

import type { SessionInfo } from '../types/tracking.types';
import { SESSION_TIMEOUT } from '../config/page-thresholds';

class SessionManager {
    private session = $state<SessionInfo | null>(null);
    private activityTimer: number | null = null;
    private storageKey = 'tracking_session';

    constructor() {
        if (typeof window !== 'undefined') {
            this.loadSession();
            this.setupActivityTracking();
            this.setupBeforeUnload();
        }
    }

    // Computed values
    get currentSession(): SessionInfo | null {
        return this.session;
    }

    get isSessionActive(): boolean {
        return this.session?.isActive ?? false;
    }

    get sessionDuration(): number {
        if (!this.session) return 0;
        return Date.now() - this.session.startTime;
    }

    // Inicia nova sessão
    startSession(userId?: string): SessionInfo {
        const sessionId = this.generateSessionId();
        const now = Date.now();

        this.session = {
            sessionId,
            userId,
            startTime: now,
            lastActivity: now,
            pageViews: 0,
            isActive: true
        };

        this.saveSession();
        this.resetActivityTimer();

        return this.session;
    }

    // Atualiza atividade da sessão
    updateActivity(): void {
        if (!this.session) return;

        this.session.lastActivity = Date.now();
        this.session.isActive = true;

        this.saveSession();
        this.resetActivityTimer();
    }

    // Incrementa página vista
    incrementPageView(): void {
        if (!this.session) return;

        this.session.pageViews++;
        this.updateActivity();
    }

    // Finaliza sessão
    endSession(): SessionInfo | null {
        if (!this.session) return null;

        const endedSession = { ...this.session };
        endedSession.isActive = false;

        this.session = null;
        this.clearSession();
        this.clearActivityTimer();

        return endedSession;
    }

    // Verifica se sessão expirou
    private checkSessionTimeout(): void {
        if (!this.session) return;

        const timeSinceLastActivity = Date.now() - this.session.lastActivity;

        if (timeSinceLastActivity > SESSION_TIMEOUT) {
            this.endSession();
        }
    }

    // Configura tracking de atividade
    private setupActivityTracking(): void {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });

        // Verifica timeout a cada minuto
        setInterval(() => {
            this.checkSessionTimeout();
        }, 60000);
    }

    // Configura salvamento antes de sair da página
    private setupBeforeUnload(): void {
        window.addEventListener('beforeunload', () => {
            this.saveSession();
        });

        // Page Visibility API para detectar quando usuário troca de aba
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveSession();
            } else {
                this.updateActivity();
            }
        });
    }

    // Timer para marcar sessão como inativa
    private resetActivityTimer(): void {
        this.clearActivityTimer();

        this.activityTimer = window.setTimeout(() => {
            if (this.session) {
                this.session.isActive = false;
                this.saveSession();
            }
        }, SESSION_TIMEOUT);
    }

    private clearActivityTimer(): void {
        if (this.activityTimer) {
            clearTimeout(this.activityTimer);
            this.activityTimer = null;
        }
    }

    // Persistência da sessão
    private saveSession(): void {
        if (!this.session) return;

        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.session));
        } catch (error) {
            console.warn('Failed to save session:', error);
        }
    }

    private loadSession(): void {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (!stored) return;

            const session: SessionInfo = JSON.parse(stored);

            // Verifica se sessão não expirou
            const timeSinceLastActivity = Date.now() - session.lastActivity;
            if (timeSinceLastActivity < SESSION_TIMEOUT) {
                this.session = session;
                this.resetActivityTimer();
            } else {
                this.clearSession();
            }
        } catch (error) {
            console.warn('Failed to load session:', error);
            this.clearSession();
        }
    }

    private clearSession(): void {
        try {
            localStorage.removeItem(this.storageKey);
        } catch (error) {
            console.warn('Failed to clear session:', error);
        }
    }

    // Gera ID único para sessão
    private generateSessionId(): string {
        return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }

    // Método público para obter ou criar sessão
    getOrCreateSession(userId?: string): SessionInfo {
        if (!this.session || !this.session.isActive) {
            return this.startSession(userId);
        }

        // Atualiza userId se fornecido (útil quando usuário faz login durante sessão)
        if (userId && this.session.userId !== userId) {
            this.session.userId = userId;
            this.saveSession();
        }

        return this.session;
    }
}

export { SessionManager };