// Configurações específicas por ambiente

import type { TrackingConfig } from '../types/tracking.types';

/**
 * Configuração para ambiente de desenvolvimento
 */
export const DEVELOPMENT_CONFIG: Partial<TrackingConfig> = {
    debugMode: true,
    batchSize: 1, // Envio imediato para debug
    flushInterval: 5000, // 5 segundos
    sessionTimeout: 5 * 60 * 1000, // 5 minutos para testes
    enablePageTracking: true,
    enableInteractionTracking: true,
    enableFormTracking: true,
    enableScrollTracking: true,
    enableNavigationTracking: true,
    enableClickTracking: true
};

/**
 * Configuração para ambiente de produção
 */
export const PRODUCTION_CONFIG: Partial<TrackingConfig> = {
    debugMode: false,
    batchSize: 10,
    flushInterval: 30000, // 30 segundos
    sessionTimeout: 30 * 60 * 1000, // 30 minutos
    enablePageTracking: true,
    enableInteractionTracking: true,
    enableFormTracking: true,
    enableScrollTracking: true,
    enableNavigationTracking: true,
    enableClickTracking: true
};

/**
 * Configuração para ambiente de staging
 */
export const STAGING_CONFIG: Partial<TrackingConfig> = {
    debugMode: true,
    batchSize: 5,
    flushInterval: 15000, // 15 segundos
    sessionTimeout: 20 * 60 * 1000, // 20 minutos
    enablePageTracking: true,
    enableInteractionTracking: true,
    enableFormTracking: true,
    enableScrollTracking: true,
    enableNavigationTracking: true,
    enableClickTracking: true
};

/**
 * Configuração focada em privacidade
 */
export const PRIVACY_CONFIG: Partial<TrackingConfig> = {
    debugMode: false,
    batchSize: 5,
    flushInterval: 60000, // 1 minuto
    sessionTimeout: 15 * 60 * 1000, // 15 minutos
    enablePageTracking: true,
    enableInteractionTracking: false, // Desabilitado para privacidade
    enableFormTracking: false, // Desabilitado para privacidade
    enableScrollTracking: false, // Desabilitado para privacidade
    enableNavigationTracking: true,
    enableClickTracking: false // Desabilitado para privacidade
};

/**
 * Configuração mínima (apenas navegação)
 */
export const MINIMAL_CONFIG: Partial<TrackingConfig> = {
    debugMode: false,
    batchSize: 5,
    flushInterval: 60000, // 1 minuto
    sessionTimeout: 30 * 60 * 1000, // 30 minutos
    enablePageTracking: true,
    enableInteractionTracking: false,
    enableFormTracking: false,
    enableScrollTracking: false,
    enableNavigationTracking: true,
    enableClickTracking: false
};

/**
 * Detecta automaticamente o ambiente baseado na URL
 */
export function detectEnvironment(): 'development' | 'staging' | 'production' {
    if (typeof window === 'undefined') return 'development';
    
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('dev')) {
        return 'development';
    }
    
    if (hostname.includes('staging') || hostname.includes('test')) {
        return 'staging';
    }
    
    return 'production';
}

/**
 * Retorna a configuração baseada no ambiente
 */
export function getEnvironmentConfig(): Partial<TrackingConfig> {
    const env = detectEnvironment();
    
    switch (env) {
        case 'development':
            return DEVELOPMENT_CONFIG;
        case 'staging':
            return STAGING_CONFIG;
        case 'production':
            return PRODUCTION_CONFIG;
        default:
            return DEVELOPMENT_CONFIG;
    }
}