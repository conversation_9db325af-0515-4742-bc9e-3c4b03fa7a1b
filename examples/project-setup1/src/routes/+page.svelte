<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import Icon from '$lib/components/ui/icon.svelte';
</script>

<svelte:head>
  <title>Home | MeuApp</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-4xl font-bold mb-4">Bem-vindo ao MeuApp</h1>
    <p class="text-lg text-muted-foreground mb-8">
      Seu projeto está pronto para começar!
    </p>
    
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:database" />
            PocketBase
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <p class="text-sm text-muted-foreground">
            Backend configurado e pronto para uso
          </p>
        </Card.Content>
      </Card.Root>
      
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:palette" />
            Shadcn/ui
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <p class="text-sm text-muted-foreground">
            Componentes modernos e customizáveis
          </p>
        </Card.Content>
      </Card.Root>
      
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:lightning-bolt" />
            SvelteKit
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <p class="text-sm text-muted-foreground">
            Framework fullstack otimizado
          </p>
        </Card.Content>
      </Card.Root>
    </div>
    
    <div class="mt-8 flex gap-4">
      <Button href="/auth/login">
        Fazer Login
      </Button>
      <Button variant="outline" href="/auth/register">
        Criar Conta
      </Button>
    </div>
  </div>
</div>