<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import Icon from '$lib/components/ui/icon.svelte';
  import { trackEvent, trackButtonClick, trackFeatureUsage, getCurrentTracker } from '$lib/tracking';
  import { toast } from 'svelte-sonner';

  // Exemplo de dados para demonstração
  let clickCount = 0;
  let formData = { name: '', email: '' };

  // Funções de exemplo para demonstrar o tracking
  function handleBasicTracking() {
    clickCount++;
    trackButtonClick('demo-basic', 'Basic Tracking Demo', {
      clickCount,
      timestamp: Date.now()
    });
    toast.success('Evento básico rastreado! Verifique o console.');
  }

  function handleCustomEvent() {
    trackEvent('custom_demo_event', {
      eventType: 'demonstration',
      userAction: 'custom_tracking_test',
      metadata: {
        page: '/tracking',
        feature: 'custom_events'
      }
    });
    toast.success('Evento customizado rastreado!');
  }

  function handleFeatureUsage() {
    trackFeatureUsage('tracking_demo', 'feature_test', {
      featureCategory: 'analytics',
      userLevel: 'demo_user'
    });
    toast.success('Uso de feature rastreado!');
  }

  function handleFormInteraction() {
    trackEvent('form_interaction_demo', {
      formId: 'demo-form',
      fieldCount: Object.keys(formData).length,
      filledFields: Object.values(formData).filter(v => v.trim()).length
    });
    toast.success('Interação de formulário rastreada!');
  }

  function showCurrentMetrics() {
    const tracker = getCurrentTracker();
    if (tracker) {
      const metrics = tracker.getCurrentPageMetrics();
      const session = tracker.getSessionInfo();
      
      console.log('📊 Métricas atuais da página:', metrics);
      console.log('👤 Informações da sessão:', session);
      
      toast.success('Métricas exibidas no console!');
    }
  }
</script>

<svelte:head>
  <title>Tracking System Demo | MeuApp</title>
  <meta name="description" content="Demonstração e exemplos do sistema de tracking integrado" />
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-4xl font-bold mb-4 flex items-center gap-3">
        <Icon icon="mdi:chart-line" class="text-blue-600" />
        Sistema de Tracking
      </h1>
      <p class="text-lg text-muted-foreground">
        Demonstração e exemplos práticos do sistema de tracking integrado ao seu projeto.
      </p>
    </div>

    <!-- Status do Sistema -->
    <Card.Root class="mb-8 border-green-200 bg-green-50">
      <Card.Header>
        <Card.Title class="text-green-800 flex items-center gap-2">
          <Icon icon="mdi:check-circle" />
          Sistema Ativo
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <p class="text-green-700">
          O tracking system está funcionando automaticamente. Todos os eventos desta página estão sendo rastreados.
        </p>
      </Card.Content>
    </Card.Root>

    <!-- Exemplos Interativos -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
      <!-- Tracking Básico -->
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:cursor-default-click" />
            Tracking Básico
          </Card.Title>
        </Card.Header>
        <Card.Content class="space-y-4">
          <p class="text-sm text-muted-foreground">
            Exemplo de tracking de cliques em botões com metadata.
          </p>
          <Button on:click={handleBasicTracking} class="w-full">
            Clique Aqui ({clickCount})
          </Button>
          <code class="text-xs bg-gray-100 p-2 rounded block">
            trackButtonClick('demo-basic', 'Basic Tracking Demo')
          </code>
        </Card.Content>
      </Card.Root>

      <!-- Eventos Customizados -->
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:code-braces" />
            Eventos Customizados
          </Card.Title>
        </Card.Header>
        <Card.Content class="space-y-4">
          <p class="text-sm text-muted-foreground">
            Crie eventos personalizados com metadata específica.
          </p>
          <Button on:click={handleCustomEvent} variant="outline" class="w-full">
            Evento Customizado
          </Button>
          <code class="text-xs bg-gray-100 p-2 rounded block">
            trackEvent('custom_demo_event', metadata)
          </code>
        </Card.Content>
      </Card.Root>

      <!-- Feature Usage -->
      <Card.Root>
        <Card.Header>
          <Card.Title class="flex items-center gap-2">
            <Icon icon="mdi:feature-search" />
            Uso de Features
          </Card.Title>
        </Card.Header>
        <Card.Content class="space-y-4">
          <p class="text-sm text-muted-foreground">
            Rastreie o uso de funcionalidades específicas.
          </p>
          <Button on:click={handleFeatureUsage} variant="secondary" class="w-full">
            Testar Feature
          </Button>
          <code class="text-xs bg-gray-100 p-2 rounded block">
            trackFeatureUsage('feature', 'action')
          </code>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Exemplo de Formulário -->
    <Card.Root class="mb-8">
      <Card.Header>
        <Card.Title class="flex items-center gap-2">
          <Icon icon="mdi:form-select" />
          Tracking de Formulários
        </Card.Title>
      </Card.Header>
      <Card.Content>
        <p class="text-sm text-muted-foreground mb-4">
          Interações com formulários são rastreadas automaticamente. Teste preenchendo os campos abaixo:
        </p>
        <div class="grid gap-4 md:grid-cols-2">
          <div>
            <label for="demo-name" class="block text-sm font-medium mb-1">Nome</label>
            <input
              id="demo-name"
              type="text"
              bind:value={formData.name}
              on:input={handleFormInteraction}
              class="w-full px-3 py-2 border rounded-md"
              placeholder="Seu nome"
            />
          </div>
          <div>
            <label for="demo-email" class="block text-sm font-medium mb-1">Email</label>
            <input
              id="demo-email"
              type="email"
              bind:value={formData.email}
              on:input={handleFormInteraction}
              class="w-full px-3 py-2 border rounded-md"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </Card.Content>
    </Card.Root>

    <!-- Métricas e Debug -->
    <Card.Root class="mb-8">
      <Card.Header>
        <Card.Title class="flex items-center gap-2">
          <Icon icon="mdi:chart-box" />
          Métricas e Debug
        </Card.Title>
      </Card.Header>
      <Card.Content class="space-y-4">
        <p class="text-sm text-muted-foreground">
          Visualize as métricas atuais da página e informações da sessão.
        </p>
        <Button on:click={showCurrentMetrics} variant="outline">
          <Icon icon="mdi:console" class="mr-2" />
          Mostrar Métricas no Console
        </Button>
      </Card.Content>
    </Card.Root>

    <!-- Documentação -->
    <Card.Root>
      <Card.Header>
        <Card.Title class="flex items-center gap-2">
          <Icon icon="mdi:book-open" />
          Documentação e Próximos Passos
        </Card.Title>
      </Card.Header>
      <Card.Content class="space-y-4">
        <div class="grid gap-4 md:grid-cols-2">
          <div>
            <h4 class="font-semibold mb-2">📚 Documentação Completa</h4>
            <p class="text-sm text-muted-foreground mb-2">
              Consulte o README completo em:
            </p>
            <code class="text-xs bg-gray-100 p-2 rounded block">
              src/lib/tracking/README.md
            </code>
          </div>
          <div>
            <h4 class="font-semibold mb-2">🔧 Configuração</h4>
            <p class="text-sm text-muted-foreground mb-2">
              Personalize o tracking em:
            </p>
            <code class="text-xs bg-gray-100 p-2 rounded block">
              src/routes/+layout.svelte
            </code>
          </div>
        </div>
        
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 class="font-semibold text-blue-800 mb-2">💡 Dicas Importantes</h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• Configure a URL do PocketBase em produção no layout</li>
            <li>• Use presets diferentes para dev/prod/privacy</li>
            <li>• Ajuste os pageThresholds baseado no seu conteúdo</li>
            <li>• Monitore o console em modo debug para ver os eventos</li>
          </ul>
        </div>
      </Card.Content>
    </Card.Root>
  </div>
</div>